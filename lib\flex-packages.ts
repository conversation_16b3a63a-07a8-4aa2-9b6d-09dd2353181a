"use server";

import { createAdminClient } from "./supabase/admin";
import type {
  FlexPackage,
  MemberFlexPackage,
  MemberFlexPackageInsert,
  MemberFlexPackageWithDetails,
  FlexPackageSession,
  FlexPackageSessionInsert,
  FlexPackageSessionWithDetails,
} from "./supabase/types";

/**
 * Esnek paket sistemini yöneten server actions
 * - Randevu alındığında direkt used_sessions artar
 * - 12 saat öncesine kadar iptal/değişiklik mümkün
 * - Gelirse/gelmezse fark etmez, randevu kullanılmış sayılır
 */

/**
 * Tüm aktif paket tiplerini getir
 */
export async function getFlexPackages(): Promise<FlexPackage[]> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("flex_packages")
    .select("*")
    .eq("is_active", true)
    .order("duration_days", { ascending: true });

  if (error) {
    throw new Error(`Paket tipleri getirilemedi: ${error.message}`);
  }

  return data;
}

/**
 * Üyenin aktif paketlerini getir
 */
export async function getMemberActivePackages(
  memberId: string
): Promise<MemberFlexPackageWithDetails[]> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("member_flex_packages")
    .select(
      `
        *,
        flex_package:flex_packages(*),
        member:members(*)
      `
    )
    .eq("member_id", memberId)
    .eq("status", "active")
    .gte("expiry_date", new Date().toISOString().split("T")[0])
    .order("expiry_date", { ascending: true });

  if (error) {
    throw new Error(`Üye paketleri getirilemedi: ${error.message}`);
  }

  return data as MemberFlexPackageWithDetails[];
}

/**
 * Yeni paket satın alma
 */
export async function purchasePackage(
  memberId: string,
  flexPackageId: string,
  startDate: string,
  paidAmount: number
): Promise<MemberFlexPackage> {
  const supabase = createAdminClient();
  // Önce paket bilgilerini al
  const { data: packageData, error: packageError } = await supabase
    .from("flex_packages")
    .select("*")
    .eq("id", flexPackageId)
    .single();

  if (packageError) {
    throw new Error(`Paket bulunamadı: ${packageError.message}`);
  }

  // Bitiş tarihini hesapla
  const startDateObj = new Date(startDate);
  const expiryDate = new Date(startDateObj);
  expiryDate.setDate(expiryDate.getDate() + packageData.duration_days);

  const memberPackage: MemberFlexPackageInsert = {
    member_id: memberId,
    flex_package_id: flexPackageId,
    start_date: startDate,
    expiry_date: expiryDate.toISOString().split("T")[0],
    total_sessions: packageData.session_count,
    paid_amount: paidAmount,
    original_price: packageData.price,
  };

  const { data, error } = await supabase
    .from("member_flex_packages")
    .insert(memberPackage)
    .select()
    .single();

  if (error) {
    throw new Error(`Paket satın alınamadı: ${error.message}`);
  }

  return data;
}

/**
 * Belirli bir tarihte üyenin zaten randevusu olup olmadığını kontrol et
 */
export async function checkExistingAppointment(
  memberId: string,
  sessionDate: string
): Promise<boolean> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("flex_package_sessions")
    .select("id")
    .eq("member_id", memberId)
    .eq("session_date", sessionDate)
    .eq("status", "scheduled")
    .limit(1);

  if (error) {
    throw new Error(`Randevu kontrolü yapılamadı: ${error.message}`);
  }

  return (data?.length || 0) > 0;
}

/**
 * Belirli bir tarih ve saat için kapasite kontrolü (maksimum 3 randevu)
 */
export async function checkTimeSlotCapacity(
  sessionDate: string,
  sessionTime: string
): Promise<{
  available: boolean;
  currentCount: number;
  maxCapacity: number;
}> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("flex_package_sessions")
    .select("id")
    .eq("session_date", sessionDate)
    .eq("session_time", sessionTime)
    .eq("status", "scheduled");

  if (error) {
    throw new Error(`Kapasite kontrolü yapılamadı: ${error.message}`);
  }

  const currentCount = data?.length || 0;
  const maxCapacity = 3;
  const available = currentCount < maxCapacity;

  return {
    available,
    currentCount,
    maxCapacity,
  };
}

/**
 * Randevu alma (direkt used_sessions artar) - Duplicate kontrolü ile
 */
export async function bookSession(
  memberId: string,
  memberPackageId: string,
  sessionDate: string,
  sessionTime: string
): Promise<FlexPackageSession> {
  const supabase = createAdminClient();
  const session: FlexPackageSessionInsert = {
    member_id: memberId,
    member_package_id: memberPackageId,
    session_date: sessionDate,
    session_time: sessionTime,
  };

  const { data, error } = await supabase
    .from("flex_package_sessions")
    .insert(session)
    .select()
    .single();

  if (error) {
    throw new Error(`Randevu alınamadı: ${error.message}`);
  }

  return data;
}

/**
 * Çoklu randevu alma - Tek seferde birden fazla randevu oluşturur
 */
export async function bookMultipleSessions(
  memberId: string,
  memberPackageId: string,
  appointments: Array<{ date: string; time: string }>
): Promise<{
  success: FlexPackageSession[];
  failed: Array<{ date: string; time: string; error: string }>;
}> {
  const success: FlexPackageSession[] = [];
  const failed: Array<{ date: string; time: string; error: string }> = [];

  // Her randevu için sırayla işlem yap (transaction benzeri davranış için)
  for (const appointment of appointments) {
    try {
      // Kapasite kontrolü
      const capacityCheck = await checkTimeSlotCapacity(
        appointment.date,
        appointment.time
      );

      if (!capacityCheck.available) {
        failed.push({
          date: appointment.date,
          time: appointment.time,
          error: `Kapasite dolu (${capacityCheck.currentCount}/${capacityCheck.maxCapacity})`,
        });
        continue;
      }

      // Aynı güne randevu kontrolü
      const hasExistingAppointment = await checkExistingAppointment(
        memberId,
        appointment.date
      );

      if (hasExistingAppointment) {
        failed.push({
          date: appointment.date,
          time: appointment.time,
          error: "Bu tarihte zaten randevunuz var",
        });
        continue;
      }

      // Randevu oluştur
      const session = await bookSession(
        memberId,
        memberPackageId,
        appointment.date,
        appointment.time
      );

      success.push(session);
    } catch (error) {
      failed.push({
        date: appointment.date,
        time: appointment.time,
        error: error instanceof Error ? error.message : "Bilinmeyen hata",
      });
    }
  }

  return { success, failed };
}

/**
 * Randevu silme (12 saat kuralı kontrollü) - İptal yerine silme
 */
export async function deleteSession(sessionId: string): Promise<void> {
  const supabase = createAdminClient();
  const { error } = await supabase
    .from("flex_package_sessions")
    .delete()
    .eq("id", sessionId);

  if (error) {
    throw new Error(`Randevu silinemedi: ${error.message}`);
  }
}

/**
 * Üyenin tüm randevularını getir
 */
export async function getMemberSessions(
  memberId: string,
  status?: "scheduled" | "completed"
): Promise<FlexPackageSessionWithDetails[]> {
  const supabase = createAdminClient();
  let query = supabase
    .from("flex_package_sessions")
    .select(
      `
        *,
        member_package:member_flex_packages(
          *,
          flex_package:flex_packages(*)
        ),
        member:members(*)
      `
    )
    .eq("member_id", memberId)
    .order("session_date", { ascending: false })
    .order("session_time", { ascending: false });

  if (status) {
    query = query.eq("status", status);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Randevular getirilemedi: ${error.message}`);
  }

  return data as FlexPackageSessionWithDetails[];
}

/**
 * Yaklaşan randevuları getir
 */
export async function getUpcomingSessions(
  memberId: string
): Promise<FlexPackageSessionWithDetails[]> {
  const supabase = createAdminClient();
  const today = new Date().toISOString().split("T")[0];

  const { data, error } = await supabase
    .from("flex_package_sessions")
    .select(
      `
        *,
        member_package:member_flex_packages(
          *,
          flex_package:flex_packages(*)
        )
      `
    )
    .eq("member_id", memberId)
    .eq("status", "scheduled")
    .gte("session_date", today)
    .order("session_date", { ascending: true })
    .order("session_time", { ascending: true });

  if (error) {
    throw new Error(`Yaklaşan randevular getirilemedi: ${error.message}`);
  }

  return data as FlexPackageSessionWithDetails[];
}

/**
 * Paket istatistikleri
 */
export async function getPackageStats(memberPackageId: string) {
  const supabase = createAdminClient();
  const { data: packageData, error: packageError } = await supabase
    .from("member_flex_packages")
    .select("*")
    .eq("id", memberPackageId)
    .single();

  if (packageError) {
    throw new Error(`Paket bulunamadı: ${packageError.message}`);
  }

  const { data: sessions, error: sessionsError } = await supabase
    .from("flex_package_sessions")
    .select("status")
    .eq("member_package_id", memberPackageId);

  if (sessionsError) {
    throw new Error(`Seans istatistikleri alınamadı: ${sessionsError.message}`);
  }

  const stats = {
    totalSessions: packageData.total_sessions,
    usedSessions: packageData.used_sessions,
    remainingSessions: packageData.total_sessions - packageData.used_sessions,
    scheduledSessions: sessions.filter((s) => s.status === "scheduled").length,
    completedSessions: sessions.filter((s) => s.status === "completed").length,
    expiryDate: packageData.expiry_date,
    status: packageData.status,
  };

  return stats;
}
