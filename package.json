{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint ."}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^4.1.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}