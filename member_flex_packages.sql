-- Member Flex Packages Table
-- Bu tablo üyelerin satın aldı<PERSON>ı esnek paketleri takip eder

CREATE TABLE member_flex_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Relationships
  member_id UUID NOT NULL,
  flex_package_id UUID NOT NULL REFERENCES flex_packages(id) ON DELETE RESTRICT,
  
  -- Purchase Information
  purchase_date TIMESTAMP DEFAULT NOW(),
  start_date DATE NOT NULL, -- <PERSON><PERSON> kull<PERSON> başlangıcı
  expiry_date DATE NOT NULL, -- Otomatik hesaplanır: start_date + duration_days
  
  -- Session Tracking
  total_sessions INTEGER NOT NULL, -- flex_packages'dan kopyalanır
  used_sessions INTEGER DEFAULT 0,
  remaining_sessions INTEGER GENERATED ALWAYS AS (total_sessions - used_sessions) STORED,
  
  -- Payment Information
  paid_amount DECIMAL(10,2) NOT NULL, -- Gerçek ödenen miktar (indirim olabilir)
  original_price DECIMAL(10,2) NOT NULL, -- Orijinal paket fiyatı
  
  -- Status
  status VARCHAR(20) CHECK (status IN ('active', 'expired', 'completed', 'cancelled', 'paused')) DEFAULT 'active',
  notes TEXT, -- Özel notlar
  -- Indexes for performance
  UNIQUE(member_id, flex_package_id, purchase_date)
);

-- Indexes
CREATE INDEX idx_member_flex_packages_member_id ON member_flex_packages(member_id);
CREATE INDEX idx_member_flex_packages_status ON member_flex_packages(status);
CREATE INDEX idx_member_flex_packages_expiry ON member_flex_packages(expiry_date);
CREATE INDEX idx_member_flex_packages_active ON member_flex_packages(member_id, status) WHERE status = 'active';

-- Trigger to auto-calculate expiry_date
CREATE OR REPLACE FUNCTION calculate_expiry_date()
RETURNS TRIGGER AS $$
BEGIN
  -- flex_packages tablosundan duration_days'i al ve expiry_date'i hesapla
  SELECT NEW.start_date + (fp.duration_days || ' days')::INTERVAL
  INTO NEW.expiry_date
  FROM flex_packages fp
  WHERE fp.id = NEW.flex_package_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_calculate_expiry_date
  BEFORE INSERT OR UPDATE ON member_flex_packages
  FOR EACH ROW
  EXECUTE FUNCTION calculate_expiry_date();

-- Trigger to auto-update remaining sessions
CREATE OR REPLACE FUNCTION update_package_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Eğer tüm seanslar kullanıldıysa paket durumunu 'completed' yap
  IF NEW.used_sessions >= NEW.total_sessions THEN
    NEW.status = 'completed';
  END IF;
  
  -- Eğer paket süresi dolmuşsa ve aktifse 'expired' yap
  IF NEW.expiry_date < CURRENT_DATE AND NEW.status = 'active' THEN
    NEW.status = 'expired';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_package_status
  BEFORE UPDATE ON member_flex_packages
  FOR EACH ROW
  EXECUTE FUNCTION update_package_status();