"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Phone, ArrowRight, Lock } from "lucide-react";
import { getMemberFromPhone } from "@/lib/actions/database";

interface ModernPhoneGateProps {
  onPhoneSubmit: (phone: string) => void;
}

export default function ModernPhoneGate({
  onPhoneSubmit,
}: ModernPhoneGateProps) {
  const [phone, setPhone] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Always use local TR number: exactly last 10 digits (e.g. 5xx xxx xx xx)
    const raw = phone.replace(/\D/g, "");
    const local10 = raw.slice(-10);
    if (local10.length !== 10) return;

    setIsLoading(true);
    try {
      const member = await getMemberFromPhone(local10);
      if (!member) {
        toast.error("Bu telefon numarası ile kayıtlı üye bulunamadı.");
        return;
      }
      onPhoneSubmit(local10);
    } catch {
      toast.error("Üye kontrolü yapılırken bir hata oluştu.");
    } finally {
      setIsLoading(false);
    }
  };

  const formatPhoneNumber = (value: string) => {
    // Value here is our normalized state (max 10 digits). Do not let '+90' leak into digits.
    const numbers = value.replace(/\D/g, "").slice(0, 10);

    if (numbers.length === 0) return "";

    // Progressive formatting as user types for TR local format with +90 prefix
    if (numbers.length <= 3) {
      return `+90 (${numbers}`;
    }

    if (numbers.length <= 6) {
      return `+90 (${numbers.slice(0, 3)}) ${numbers.slice(3)}`;
    }

    if (numbers.length <= 8) {
      return `+90 (${numbers.slice(0, 3)}) ${numbers.slice(
        3,
        6
      )} ${numbers.slice(6)}`;
    }

    // Complete format: +90 (555) 123 45 67
    return `+90 (${numbers.slice(0, 3)}) ${numbers.slice(3, 6)} ${numbers.slice(
      6,
      8
    )} ${numbers.slice(8)}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/10 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="w-full max-w-md"
      >
        <div className="text-center space-y-8">
          <div className="space-y-4">
            <motion.div
              className="w-16 h-16 mx-auto bg-gradient-to-r from-violet-500 to-purple-500 rounded-2xl flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <Phone className="w-8 h-8 text-white" />
            </motion.div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Üye Girişi</h1>
              <p className="text-muted-foreground mt-2">
                Randevu panelinize erişmek için telefon numaranızı girin
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <div className="relative">
                <Input
                  type="tel"
                  placeholder="+90 (555) 123 45 67"
                  value={formatPhoneNumber(phone)}
                  onChange={(e) => {
                    let raw = e.target.value.replace(/\D/g, "");
                    // If user is typing into the formatted input, it will include '+90' digits; strip them
                    if (raw.startsWith("90")) raw = raw.slice(2);
                    // Also strip a single leading '0' if present (e.g., 05xx...)
                    if (raw.startsWith("0")) raw = raw.slice(1);
                    // Cap to 10 digits
                    const normalized = raw.slice(0, 10);
                    setPhone(normalized);
                  }}
                  className="h-14 text-center text-lg border-border/50 bg-secondary/20 focus:bg-background transition-colors pl-12"
                  required
                />
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              </div>
              <p className="text-xs text-muted-foreground">
                Numaranız sadece üyelik doğrulaması için kullanılacaktır
              </p>
            </div>

            <Button
              type="submit"
              size="lg"
              variant="gradient"
              disabled={phone.length < 10 || isLoading}
              className="w-full py-4 text-lg group"
            >
              {isLoading ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"
                />
              ) : (
                <>
                  Giriş Yap
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </>
              )}
              {isLoading ? "Kontrol Ediliyor..." : "Giriş Yap"}
            </Button>
          </form>

          <div className="text-center text-xs text-muted-foreground pt-4">
            <p>Loca Fit Studio © {new Date().getFullYear()}</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
