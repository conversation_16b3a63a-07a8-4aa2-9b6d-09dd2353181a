"use client";

import { useState, useEffect } from "react";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
} from "@/lib/actions/database";
import type { Member } from "@/lib/supabase/types";
import NewModernLayout from "@/components/appointments/NewModernLayout";
import NewModernPhoneGate from "@/components/appointments/NewModernPhoneGate";

export default function AppointmentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [userPhone, setUserPhone] = useState<string | null>(null);
  const [memberInfo, setMemberInfo] = useState<Member | null>(null);

  useEffect(() => {
    const storedPhone = sessionStorage.getItem("memberPhone");
    if (storedPhone) {
      setUserPhone(storedPhone);
      fetchMemberInfo(storedPhone);
    }
  }, []);

  const fetchMemberInfo = async (phone: string) => {
    try {
      await autoCompletePastSessions();

      const member = await getMemberFromPhone(phone);
      if (member) {
        setMemberInfo(member);
      }
    } catch (error) {
      console.error("Error fetching member info:", error);
    } finally {
    }
  };

  const handlePhoneSubmit = (phone: string) => {
    sessionStorage.setItem("memberPhone", phone);
    setUserPhone(phone);
    fetchMemberInfo(phone);
  };

  const handleLogout = () => {
    sessionStorage.removeItem("memberPhone");
    setUserPhone(null);
    setMemberInfo(null);
  };

  // If no phone, show login gate
  if (!userPhone) {
    return <NewModernPhoneGate onPhoneSubmit={handlePhoneSubmit} />;
  }

  return (
    <NewModernLayout memberInfo={memberInfo} onLogout={handleLogout}>
      {children}
    </NewModernLayout>
  );
}
