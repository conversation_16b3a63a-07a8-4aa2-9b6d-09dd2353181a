"use client";

import { useState } from "react";
import CalendarDatePicker from "@/components/appointments/shared/CalendarDatePicker";

export default function TestCalendarPage() {
  const [selectedDate, setSelectedDate] = useState("");
  const [bookedDates] = useState<string[]>([]);

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Test Calendar Date Picker</h1>

        <div className="bg-card border rounded-xl p-6 shadow-lg">
          <CalendarDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            bookedDates={bookedDates}
          />

          {selectedDate && (
            <div className="mt-8 p-4 bg-secondary/50 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Selected Date</h2>
              <p className="text-lg">{selectedDate}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
