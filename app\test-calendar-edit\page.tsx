"use client";

import { useState } from "react";
import CalendarDatePicker from "@/components/appointments/shared/CalendarDatePicker";

export default function TestCalendarEditPage() {
  const [selectedDate, setSelectedDate] = useState("2025-09-15");
  const [bookedDates] = useState<string[]>(["2025-09-16", "2025-09-17"]);
  const originalDate = "2025-09-15";

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          Test Calendar Date Picker - Edit Mode
        </h1>

        <div className="bg-card border rounded-xl p-6 shadow-lg">
          <CalendarDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            bookedDates={bookedDates}
            originalDate={originalDate}
            isEditMode={true}
          />

          {selectedDate && (
            <div className="mt-8 p-4 bg-secondary/50 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Selected Date</h2>
              <p className="text-lg">{selectedDate}</p>

              {selectedDate !== originalDate && (
                <p className="text-sm text-muted-foreground mt-2">
                  Note: This is different from the original date ({originalDate}
                  )
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
