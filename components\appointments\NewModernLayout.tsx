"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import Link from "next/link";
import { Du<PERSON><PERSON>, User, LogOut, Calendar, Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Member } from "@/lib/supabase/types";


interface NewModernLayoutProps {
  children: React.ReactNode;
  memberInfo: Member | null;
  onLogout: () => void;
}

export default function NewModernLayout({
  children,
  memberInfo,
  onLogout,
}: NewModernLayoutProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Modern Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "fixed top-0 left-0 right-0 z-50 transition-all duration-300 backdrop-blur-xl",
          isScrolled
            ? "bg-background/80 border-b border-border/30 py-2 shadow-sm"
            : "bg-transparent py-4"
        )}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="flex items-center justify-between">
            {/* Brand */}
            <Link href="/" className="flex items-center gap-3 group">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary/80 to-primary flex items-center justify-center shadow-md">
                <Dumbbell className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                LocaFit
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-6">
              <nav className="flex items-center gap-1">
                <Link
                  href="/appointments"
                  className="px-4 py-2 rounded-lg text-foreground font-medium hover:bg-secondary/50 transition-colors"
                >
                  Randevularım
                </Link>
                <Link
                  href="/appointments/new"
                  className="px-4 py-2 rounded-lg text-foreground font-medium hover:bg-secondary/50 transition-colors"
                >
                  Yeni Randevu
                </Link>
              </nav>
            </div>

            {/* Profile and Actions */}
            <div className="flex items-center gap-3">
              <div className="hidden sm:flex items-center gap-3 px-3 py-2 bg-secondary/30 backdrop-blur-sm rounded-lg">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary/60 to-primary/40 flex items-center justify-center">
                  <User className="w-4 h-4 text-primary-foreground" />
                </div>
                <span className="text-sm font-medium text-foreground">
                  {memberInfo?.first_name || "Üye"}
                </span>
              </div>
              <ThemeSwitcher />
              <Button
                variant="ghost"
                size="icon"
                onClick={onLogout}
                className="hover:bg-destructive/10 hover:text-destructive"
              >
                <LogOut className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="md:hidden mt-4 pb-4 border-t border-border/30"
              >
                <nav className="flex flex-col gap-2 mt-4">
                  <Link
                    href="/appointments"
                    className="px-4 py-3 rounded-lg text-foreground font-medium hover:bg-secondary/50 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Randevularım
                  </Link>
                  <Link
                    href="/appointments/new"
                    className="px-4 py-3 rounded-lg text-foreground font-medium hover:bg-secondary/50 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Yeni Randevu
                  </Link>
                </nav>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="pt-20 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">{children}</div>
      </main>

      {/* Floating Action Button for Mobile */}
      <div className="md:hidden fixed bottom-6 right-6 z-40">
        <Link href="/appointments/new">
          <Button
            size="lg"
            className="rounded-full w-14 h-14 p-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/80"
          >
            <Calendar className="w-6 h-6 text-primary-foreground" />
          </Button>
        </Link>
      </div>
    </div>
  );
}
