"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberActivePackages,
  getMemberScheduledSessions,
  checkExistingAppointment,
  checkTimeSlotCapacity,
  bookSession,
  bookMultipleAppointments,
} from "@/lib/actions/database";
import type { FlexPackage, MemberFlexPackage } from "@/lib/supabase/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  Package,
  Plus,
  ArrowLeft,
  CheckCircle2,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  formatDate,
  isTimeSlotInPast,
} from "@/components/appointments/shared/appointment-utils";
import DatePicker from "@/components/appointments/shared/DatePicker";
import TimeSlotPicker from "@/components/appointments/shared/TimeSlotPicker";
import MultipleAppointmentPicker from "@/components/appointments/shared/MultipleAppointmentPicker";
import { useAppointmentCapacity } from "@/components/appointments/shared/AppointmentCapacityHook";

interface MemberPackageWithDetails extends MemberFlexPackage {
  flex_package?: FlexPackage;
}

export default function NewAppointmentPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [memberPackages, setMemberPackages] = useState<
    MemberPackageWithDetails[]
  >([]);
  const [selectedPackage, setSelectedPackage] =
    useState<MemberPackageWithDetails | null>(null);
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  // Çoklu randevu için yeni state'ler
  const [isMultipleMode, setIsMultipleMode] = useState(false);
  const [selectedAppointments, setSelectedAppointments] = useState<
    Array<{ date: string; time: string }>
  >([]);
  const [loading, setLoading] = useState(true);
  const [booking, setBooking] = useState(false);
  const [memberPhone, setMemberPhone] = useState("");
  const [bookedDates, setBookedDates] = useState<string[]>([]);

  // Use shared appointment capacity hook
  const { timeSlotCapacity, loadingTimeSlots, fetchTimeSlotCapacities } =
    useAppointmentCapacity();

  // Step navigation functions
  const goToNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceedToNextStep = () => {
    if (currentStep === 1) return selectedPackage !== null;
    if (currentStep === 2) {
      if (isMultipleMode) {
        return selectedAppointments.length > 0;
      } else {
        return selectedDate !== "" && selectedTime !== "";
      }
    }
    return false;
  };

  useEffect(() => {
    // Session storage'dan telefon numarasını al
    const phone = sessionStorage.getItem("memberPhone");
    if (!phone) {
      window.location.href = "/appointments";
      return;
    }
    setMemberPhone(phone);
    fetchMemberPackages(phone);
  }, []);

  async function fetchMemberPackages(phone: string) {
    try {
      // Auto-complete past sessions first to ensure current status
      await autoCompletePastSessions();

      const member = await getMemberFromPhone(phone);

      if (!member) {
        console.error("Member not found:" + phone);
        setLoading(false);
        return;
      }

      // Member'ın aktif paketlerini getir
      const packages = await getMemberActivePackages(member.id);
      setMemberPackages(packages);

      // Member'ın mevcut randevularını getir (sadece scheduled olanlar)
      const bookedSessionDates = await getMemberScheduledSessions(member.id);
      setBookedDates(bookedSessionDates);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  async function handleBookAppointment() {
    if (!selectedPackage || !selectedDate || !selectedTime) {
      toast.error("Lütfen tüm alanları doldurun");
      return;
    }

    setBooking(true);
    try {
      // Otomatik session tamamlama
      await autoCompletePastSessions();

      // Member ID'yi al
      const member = await getMemberFromPhone(memberPhone);
      if (!member) {
        toast.error("Üye bulunamadı");
        return;
      }

      // Aynı güne randevu kontrolü
      const hasExistingAppointment = await checkExistingAppointment(
        member.id,
        selectedDate
      );

      if (hasExistingAppointment) {
        toast.error(
          "Bu tarihte zaten bir randevunuz bulunmaktadır. Aynı güne birden fazla randevu alamazsınız."
        );
        return;
      }

      // Zaman dilimi kapasite kontrolü (maksimum 3 randevu)
      const capacityCheck = await checkTimeSlotCapacity(
        selectedDate,
        selectedTime
      );

      if (!capacityCheck.available) {
        toast.error(
          `Bu tarih ve saat için kapasite dolu. Şu anda ${capacityCheck.currentCount}/${capacityCheck.maxCapacity} randevu mevcut.`
        );
        return;
      }

      // Geçmiş zaman kontrolü
      if (isTimeSlotInPast(selectedDate, selectedTime)) {
        toast.error(
          "Geçmiş saatlere randevu alamazsınız. Lütfen gelecek bir zaman seçin."
        );
        return;
      }

      // Randevu oluştur
      const session = await bookSession(
        member.id,
        selectedPackage.id,
        selectedDate,
        selectedTime
      );

      if (!session) {
        toast.error("Randevu oluşturulurken hata oluştu");
        return;
      }

      toast.success("Randevu başarıyla oluşturuldu!");
      // Ana sayfaya yönlendir
      setTimeout(() => {
        window.location.href = "/appointments";
      }, 1500);
    } catch (error) {
      console.error("Booking error:", error);
      toast.error("Randevu oluşturulurken hata oluştu");
    } finally {
      setBooking(false);
    }
  }

  async function handleBookMultipleAppointments() {
    if (!selectedPackage || selectedAppointments.length === 0) {
      toast.error("Lütfen paket seçin ve en az bir randevu ekleyin");
      return;
    }

    setBooking(true);
    try {
      // Otomatik session tamamlama
      await autoCompletePastSessions();

      // Member ID'yi al
      const member = await getMemberFromPhone(memberPhone);
      if (!member) {
        toast.error("Üye bulunamadı");
        return;
      }

      // Çoklu randevu oluştur
      const result = await bookMultipleAppointments(
        member.id,
        selectedPackage.id,
        selectedAppointments
      );

      if (result.success.length > 0) {
        toast.success(
          `${result.success.length} randevu başarıyla oluşturuldu!`
        );
      }

      if (result.failed.length > 0) {
        const failedMessages = result.failed.map(
          (f) => `${formatDate(f.date)} ${f.time}: ${f.error}`
        );
        toast.error(
          `${
            result.failed.length
          } randevu oluşturulamadı:\n${failedMessages.join("\n")}`
        );
      }

      // Eğer en az bir randevu başarılıysa ana sayfaya yönlendir
      if (result.success.length > 0) {
        setTimeout(() => {
          window.location.href = "/appointments";
        }, 2000);
      }
    } catch (error) {
      console.error("Multiple booking error:", error);
      toast.error("Randevular oluşturulurken hata oluştu");
    } finally {
      setBooking(false);
    }
  }

  // Tarih seçildiğinde kapasiteleri getir
  const handleDateSelection = (date: string) => {
    setSelectedDate(date);
    setSelectedTime(""); // Saat seçimini sıfırla
    fetchTimeSlotCapacities(date);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="text-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin mx-auto text-primary" />
              <div className="text-lg font-medium text-foreground">
                Paketleriniz yükleniyor...
              </div>
              <div className="text-sm text-muted-foreground">
                Lütfen bekleyin
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (memberPackages.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-secondary rounded-full flex items-center justify-center">
                <Package className="w-10 h-10 text-muted-foreground" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-destructive rounded-full flex items-center justify-center">
                <span className="text-xs text-destructive-foreground">!</span>
              </div>
            </div>
            <div className="text-center space-y-3 max-w-md">
              <h2 className="text-2xl font-bold text-foreground">
                Aktif Paketiniz Bulunmuyor
              </h2>
              <p className="text-muted-foreground leading-relaxed">
                Randevu alabilmek için önce bir paket satın almanız gerekiyor.
                Lütfen önce bir paket seçin.
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => (window.location.href = "/appointments")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Ana Sayfaya Dön
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8 pt-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Plus className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Yeni Randevu Oluştur
              </h1>
              <p className="text-muted-foreground">
                Adım {currentStep}/3:{" "}
                {currentStep === 1
                  ? "Paket Seçimi"
                  : currentStep === 2
                  ? "Tarih ve Saat Seçimi"
                  : "Önizleme"}
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => (window.location.href = "/appointments")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Geri
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",
                    currentStep >= step
                      ? "bg-primary text-white"
                      : "bg-secondary text-muted-foreground"
                  )}
                >
                  {currentStep > step ? (
                    <CheckCircle2 className="w-5 h-5" />
                  ) : (
                    step
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <div
                    className={cn(
                      "text-sm font-medium",
                      currentStep >= step
                        ? "text-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    {step === 1 && "Paket Seçimi"}
                    {step === 2 && "Tarih & Saat"}
                    {step === 3 && "Önizleme"}
                  </div>
                </div>
                {step < 3 && (
                  <div
                    className={cn(
                      "h-1 w-16 mx-4 rounded-full transition-all duration-300",
                      currentStep > step ? "bg-primary" : "bg-secondary"
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-8">
          {/* Step 1: Paket Seçimi */}
          {currentStep === 1 && (
            <Card
              variant="elevated"
              className="relative overflow-hidden border-l-4 border-l-primary/30"
            >
              <CardHeader className="bg-secondary/30">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Package className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <span className="text-lg text-foreground">
                        Paket Seçimi
                      </span>
                      <p className="text-sm text-muted-foreground font-normal">
                        Kullanmak istediğiniz paketi seçin
                      </p>
                    </div>
                  </div>
                  {selectedPackage && (
                    <CheckCircle2 className="w-5 h-5 text-emerald-500 dark:text-emerald-400" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid gap-4 mb-6">
                  {memberPackages.map((pkg) => {
                    const remainingSessions =
                      pkg.total_sessions - pkg.used_sessions;
                    const isSelected = selectedPackage?.id === pkg.id;
                    const progressPercentage =
                      (pkg.used_sessions / pkg.total_sessions) * 100;

                    return (
                      <div
                        key={pkg.id}
                        className={cn(
                          "group relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg",
                          isSelected
                            ? "border-primary bg-primary/5 shadow-md"
                            : "border-border hover:border-primary/50 bg-card"
                        )}
                        onClick={() => setSelectedPackage(pkg)}
                      >
                        {isSelected && (
                          <div className="absolute top-4 right-4">
                            <CheckCircle2 className="w-5 h-5 text-primary" />
                          </div>
                        )}

                        <div className="space-y-4">
                          <div>
                            <h3 className="font-bold text-xl text-foreground">
                              {pkg.flex_package?.name}
                            </h3>
                            <p className="text-muted-foreground text-sm mt-1">
                              {pkg.flex_package?.description}
                            </p>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-6 text-sm">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                <span className="font-medium text-emerald-600 dark:text-emerald-400">
                                  Kalan: {remainingSessions} seans
                                </span>
                              </div>
                              <div className="text-muted-foreground">
                                Son kullanma:{" "}
                                {new Date(pkg.expiry_date).toLocaleDateString(
                                  "tr-TR"
                                )}
                              </div>
                            </div>

                            <Badge
                              variant={
                                remainingSessions > 0
                                  ? "default"
                                  : "destructive"
                              }
                              className="shrink-0"
                            >
                              {remainingSessions > 0 ? "Aktif" : "Tükendi"}
                            </Badge>
                          </div>

                          {/* Progress Bar */}
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Kullanılan: {pkg.used_sessions}</span>
                              <span>Toplam: {pkg.total_sessions}</span>
                            </div>
                            <div className="w-full bg-secondary/50 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progressPercentage}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Step Navigation */}
                <div className="flex justify-end mt-6">
                  <Button
                    onClick={goToNextStep}
                    disabled={!canProceedToNextStep()}
                    className="flex items-center gap-2"
                  >
                    Devam Et
                    <ArrowLeft className="w-4 h-4 rotate-180" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Tarih ve Saat Seçimi */}
          {currentStep === 2 && (
            <Card className="relative overflow-hidden border-l-4 border-l-primary/30">
              <CardHeader className="bg-secondary/30">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Calendar className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <span className="text-lg text-foreground">
                        Randevu Seçimi
                      </span>
                      <p className="text-sm text-muted-foreground font-normal">
                        {isMultipleMode
                          ? "Birden fazla randevu seçin"
                          : "Randevu tarihinizi ve saatinizi seçin"}
                      </p>
                    </div>
                  </div>
                  {((isMultipleMode && selectedAppointments.length > 0) ||
                    (!isMultipleMode && selectedDate && selectedTime)) && (
                    <CheckCircle2 className="w-5 h-5 text-emerald-500 dark:text-emerald-400" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* Mod Seçimi */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <h3 className="text-lg font-semibold text-foreground">
                      Randevu Modu
                    </h3>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      variant={!isMultipleMode ? "default" : "outline"}
                      onClick={() => {
                        setIsMultipleMode(false);
                        setSelectedAppointments([]);
                      }}
                      className="h-auto p-4 flex flex-col items-center gap-2"
                    >
                      <Clock className="w-5 h-5" />
                      <div className="text-center">
                        <div className="font-medium">Tek Randevu</div>
                        <div className="text-xs opacity-70">
                          Bir tarih ve saat seçin
                        </div>
                      </div>
                    </Button>
                    <Button
                      variant={isMultipleMode ? "default" : "outline"}
                      onClick={() => {
                        setIsMultipleMode(true);
                        setSelectedDate("");
                        setSelectedTime("");
                      }}
                      className="h-auto p-4 flex flex-col items-center gap-2"
                    >
                      <Calendar className="w-5 h-5" />
                      <div className="text-center">
                        <div className="font-medium">Çoklu Randevu</div>
                        <div className="text-xs opacity-70">
                          Birden fazla randevu seçin
                        </div>
                      </div>
                    </Button>
                  </div>
                </div>

                {/* Tek Randevu Modu */}
                {!isMultipleMode && (
                  <div className="space-y-6">
                    {/* Tarih Seçimi */}
                    <DatePicker
                      selectedDate={selectedDate}
                      onDateSelect={handleDateSelection}
                      bookedDates={bookedDates}
                    />

                    {/* Saat Seçimi */}
                    {selectedDate && (
                      <TimeSlotPicker
                        selectedDate={selectedDate}
                        selectedTime={selectedTime}
                        onTimeSelect={setSelectedTime}
                        timeSlotCapacity={timeSlotCapacity}
                        loadingTimeSlots={loadingTimeSlots}
                      />
                    )}
                  </div>
                )}

                {/* Çoklu Randevu Modu */}
                {isMultipleMode && (
                  <MultipleAppointmentPicker
                    selectedAppointments={selectedAppointments}
                    onAppointmentsChange={setSelectedAppointments}
                    bookedDates={bookedDates}
                    timeSlotCapacity={timeSlotCapacity}
                    loadingTimeSlots={loadingTimeSlots}
                    fetchTimeSlotCapacities={fetchTimeSlotCapacities}
                    maxAppointments={
                      selectedPackage
                        ? selectedPackage.total_sessions -
                          selectedPackage.used_sessions
                        : 12
                    }
                  />
                )}

                {/* Step Navigation */}
                <div className="flex justify-between mt-6">
                  <Button
                    variant="outline"
                    onClick={goToPreviousStep}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Geri
                  </Button>
                  <Button
                    onClick={goToNextStep}
                    disabled={!canProceedToNextStep()}
                    className="flex items-center gap-2"
                  >
                    Önizleme
                    <ArrowLeft className="w-4 h-4 rotate-180" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Önizleme */}
          {currentStep === 3 &&
            selectedPackage &&
            ((isMultipleMode && selectedAppointments.length > 0) ||
              (!isMultipleMode && selectedDate && selectedTime)) && (
              <Card
                variant="elevated"
                className="relative overflow-hidden border-l-4 border-l-emerald-500"
              >
                <CardHeader className="bg-gradient-to-r from-emerald-50 to-emerald-50/50 dark:from-emerald-950/20 dark:to-emerald-950/10">
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-emerald-500/20 rounded-lg">
                      <CheckCircle2 className="w-5 h-5 text-emerald-500 dark:text-emerald-400" />
                    </div>
                    <div>
                      <span className="text-lg text-emerald-500 dark:text-emerald-400">
                        Randevu Özeti
                      </span>
                      <p className="text-sm text-emerald-500/80 dark:text-emerald-400/80 font-normal">
                        Bilgilerinizi kontrol edin
                      </p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div className="bg-secondary/50 p-6 rounded-xl border space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="text-sm text-muted-foreground">
                            Paket
                          </div>
                          <div className="font-semibold text-lg">
                            {selectedPackage.flex_package?.name}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm text-muted-foreground">
                            Telefon
                          </div>
                          <div className="font-semibold text-lg">
                            {memberPhone}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm text-muted-foreground">
                            Randevu Modu
                          </div>
                          <div className="font-semibold text-lg">
                            {isMultipleMode ? "Çoklu Randevu" : "Tek Randevu"}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm text-muted-foreground">
                            Randevu Sayısı
                          </div>
                          <div className="font-semibold text-lg">
                            {isMultipleMode ? selectedAppointments.length : 1}{" "}
                            randevu
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Randevu Detayları */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">
                        Randevu Detayları
                      </h3>
                      {isMultipleMode ? (
                        <div className="space-y-3">
                          {selectedAppointments.map((appointment, index) => (
                            <div
                              key={`${appointment.date}-${appointment.time}`}
                              className="flex items-center justify-between p-4 border rounded-lg bg-card"
                            >
                              <div className="flex items-center space-x-4">
                                <div className="p-2 bg-primary/10 rounded-lg">
                                  <Calendar className="w-4 h-4 text-primary" />
                                </div>
                                <div>
                                  <p className="font-medium text-foreground">
                                    {formatDate(appointment.date)}
                                  </p>
                                  <p className="text-sm text-muted-foreground">
                                    {appointment.time}
                                  </p>
                                </div>
                              </div>
                              <Badge variant="secondary">
                                Randevu {index + 1}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-between p-4 border rounded-lg bg-card">
                          <div className="flex items-center space-x-4">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <Calendar className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium text-foreground">
                                {formatDate(selectedDate)}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {selectedTime}
                              </p>
                            </div>
                          </div>
                          <Badge variant="secondary">Tek Randevu</Badge>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        variant="outline"
                        onClick={goToPreviousStep}
                        className="h-12 px-8"
                        size="lg"
                        disabled={booking}
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Geri
                      </Button>
                      <Button
                        onClick={
                          isMultipleMode
                            ? handleBookMultipleAppointments
                            : handleBookAppointment
                        }
                        disabled={booking}
                        className="flex-1 h-12 text-lg font-medium"
                        size="lg"
                        variant="gradient"
                      >
                        {booking ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            {isMultipleMode
                              ? "Randevular Oluşturuluyor..."
                              : "Randevu Oluşturuluyor..."}
                          </>
                        ) : (
                          <>
                            <CheckCircle2 className="w-4 h-4 mr-2" />
                            {isMultipleMode
                              ? `${selectedAppointments.length} Randevuyu Onayla`
                              : "Randevuyu Onayla"}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
        </div>
      </div>
    </div>
  );
}
