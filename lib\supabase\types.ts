export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      flex_packages: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          name: string;
          description: string | null;
          duration_days: number;
          session_count: number;
          price: number;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          name: string;
          description?: string | null;
          duration_days: number;
          session_count: number;
          price: number;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          name?: string;
          description?: string | null;
          duration_days?: number;
          session_count?: number;
          price?: number;
          is_active?: boolean;
        };
        Relationships: [];
      };
      member_flex_packages: {
        Row: {
          id: string;
          created_at: string;
          updated_at: string;
          member_id: string;
          flex_package_id: string;
          purchase_date: string;
          start_date: string;
          expiry_date: string;
          total_sessions: number;
          used_sessions: number;
          paid_amount: number;
          original_price: number;
          status: "active" | "expired" | "completed" | "cancelled" | "paused";
          notes: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          member_id: string;
          flex_package_id: string;
          purchase_date?: string;
          start_date: string;
          expiry_date: string;
          total_sessions: number;
          used_sessions?: number;
          paid_amount: number;
          original_price: number;
          status?: "active" | "expired" | "completed" | "cancelled" | "paused";
          notes?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          updated_at?: string;
          member_id?: string;
          flex_package_id?: string;
          purchase_date?: string;
          start_date?: string;
          expiry_date?: string;
          total_sessions?: number;
          used_sessions?: number;
          paid_amount?: number;
          original_price?: number;
          status?: "active" | "expired" | "completed" | "cancelled" | "paused";
          notes?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "member_flex_packages_member_id_fkey";
            columns: ["member_id"];
            isOneToOne: false;
            referencedRelation: "members";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "member_flex_packages_flex_package_id_fkey";
            columns: ["flex_package_id"];
            isOneToOne: false;
            referencedRelation: "flex_packages";
            referencedColumns: ["id"];
          }
        ];
      };
      flex_package_sessions: {
        Row: {
          id: string;
          created_at: string;
          member_id: string;
          member_package_id: string;
          session_date: string;
          session_time: string;
          status: "scheduled" | "completed" | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          member_id: string;
          member_package_id: string;
          session_date: string;
          session_time: string;
          status?: "scheduled" | "completed" | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          member_id?: string;
          member_package_id?: string;
          session_date?: string;
          session_time?: string;
          status?: "scheduled" | "completed" | null;
        };
        Relationships: [
          {
            foreignKeyName: "flex_package_sessions_member_id_fkey";
            columns: ["member_id"];
            isOneToOne: false;
            referencedRelation: "members";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "flex_package_sessions_member_package_id_fkey";
            columns: ["member_package_id"];
            isOneToOne: false;
            referencedRelation: "member_flex_packages";
            referencedColumns: ["id"];
          }
        ];
      };
      appointments: {
        Row: {
          id: string;
          created_at: string;
          member_id: string;
          trainer_id: string;
          service_id: string;
          date: string;
          time: string;
          status: "scheduled" | "in-progress" | "completed" | "cancelled";
          notes: string | null;
          updated_at: string;
        };
        Insert: {
          id?: string;
          created_at?: string;
          member_id: string;
          trainer_id: string;
          service_id: string;
          date: string;
          time: string;
          status: "scheduled" | "in-progress" | "completed" | "cancelled";
          notes?: string | null;
          updated_at?: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          member_id?: string;
          trainer_id?: string;
          service_id?: string;
          date?: string;
          time?: string;
          status?: "scheduled" | "in-progress" | "completed" | "cancelled";
          notes?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "appointments_member_id_fkey";
            columns: ["member_id"];
            isOneToOne: false;
            referencedRelation: "members";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "appointments_service_id_fkey";
            columns: ["service_id"];
            isOneToOne: false;
            referencedRelation: "services";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "appointments_trainer_id_fkey";
            columns: ["trainer_id"];
            isOneToOne: false;
            referencedRelation: "trainers";
            referencedColumns: ["id"];
          }
        ];
      };
      member_payments: {
        Row: {
          id: string;
          member_name: string;
          created_at: string;
          credit_card_paid: number;
          cash_paid: number;
          package_name: string | null;
        };
        Insert: {
          id?: string;
          member_name: string;
          created_at?: string;
          credit_card_paid?: number;
          cash_paid?: number;
          package_name?: string | null;
        };
        Update: {
          id?: string;
          member_name?: string;
          created_at?: string;
          credit_card_paid?: number;
          cash_paid?: number;
          package_name?: string | null;
        };
        Relationships: [];
      };
      members: {
        Row: {
          id: string;
          created_at: string;
          first_name: string;
          last_name: string;
          email: string | null;
          phone: string;
          membership_type: "basic" | "premium" | "vip";
          subscribed_services: string[];
          start_date: string;
          avatar_url: string | null;
          notes: string | null;
          active: boolean | null;
          postponement_count: number | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          first_name: string;
          last_name: string;
          email?: string | null;
          phone: string;
          membership_type: "basic" | "premium" | "vip";
          subscribed_services?: string[];
          start_date: string;
          avatar_url?: string | null;
          notes?: string | null;
          active?: boolean | null;
          postponement_count?: number | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          first_name?: string;
          last_name?: string;
          email?: string | null;
          phone?: string;
          membership_type?: "basic" | "premium" | "vip";
          subscribed_services?: string[];
          start_date?: string;
          avatar_url?: string | null;
          notes?: string | null;
          active?: boolean | null;
          postponement_count?: number | null;
        };
        Relationships: [];
      };
      services: {
        Row: {
          id: string;
          created_at: string;
          name: string;
          description: string;
          price: number;
          duration: number;
          max_participants: number;
          session_count: number | null;
          isVipOnly: boolean;
          active: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string;
          name: string;
          description: string;
          price: number;
          duration: number;
          max_participants: number;
          session_count?: number | null;
          isVipOnly?: boolean;
          active?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string;
          name?: string;
          description?: string;
          price?: number;
          duration?: number;
          max_participants?: number;
          session_count?: number | null;
          isVipOnly?: boolean;
          active?: boolean;
        };
        Relationships: [];
      };
      trainers: {
        Row: {
          id: string;
          created_at: string;
          first_name: string;
          last_name: string;
          email: string | null;
          phone: string;
          categories: string[];
          bio: string;
          start_date: string;
          working_hours: Json;
          status: string;
          auth_user_id: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          first_name: string;
          last_name: string;
          email?: string | null;
          phone: string;
          categories: string[];
          bio: string;
          start_date: string;
          working_hours: Json;
          status?: string;
          auth_user_id?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          first_name?: string;
          last_name?: string;
          email?: string | null;
          phone?: string;
          categories?: string[];
          bio?: string;
          start_date?: string;
          working_hours?: Json;
          status?: string;
          auth_user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "trainers_auth_user_id_fkey";
            columns: ["auth_user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      auto_complete_past_sessions: {
        Args: Record<string, never>;
        Returns: number;
      };
    };
    Enums: {
      appointment_status:
        | "scheduled"
        | "in-progress"
        | "completed"
        | "cancelled";
      trainer_status: "active" | "inactive" | "suspended";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

// Type helpers for easier usage
export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"] & Database["public"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] &
      Database["public"]["Views"])
  ? (Database["public"]["Tables"] &
      Database["public"]["Views"])[PublicTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof Database["public"]["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof Database["public"]["Enums"]
  ? Database["public"]["Enums"][PublicEnumNameOrOptions]
  : never;

// Convenience types for common operations
export type Appointment = Tables<"appointments">;
export type AppointmentInsert = TablesInsert<"appointments">;
export type AppointmentUpdate = TablesUpdate<"appointments">;

export type MemberPayment = Tables<"member_payments">;
export type MemberPaymentInsert = TablesInsert<"member_payments">;
export type MemberPaymentUpdate = TablesUpdate<"member_payments">;

export type Member = Tables<"members">;
export type MemberInsert = TablesInsert<"members">;
export type MemberUpdate = TablesUpdate<"members">;

export type Service = Tables<"services">;
export type ServiceInsert = TablesInsert<"services">;
export type ServiceUpdate = TablesUpdate<"services">;

export type Trainer = Tables<"trainers">;
export type TrainerInsert = TablesInsert<"trainers">;
export type TrainerUpdate = TablesUpdate<"trainers">;

// New Flex Package types
export type FlexPackage = Tables<"flex_packages">;
export type FlexPackageInsert = TablesInsert<"flex_packages">;
export type FlexPackageUpdate = TablesUpdate<"flex_packages">;

export type MemberFlexPackage = Tables<"member_flex_packages">;
export type MemberFlexPackageInsert = TablesInsert<"member_flex_packages">;
export type MemberFlexPackageUpdate = TablesUpdate<"member_flex_packages">;

export type FlexPackageSession = Tables<"flex_package_sessions">;
export type FlexPackageSessionInsert = TablesInsert<"flex_package_sessions">;
export type FlexPackageSessionUpdate = TablesUpdate<"flex_package_sessions">;

// Working hours type for trainers
export type WorkingHours = {
  [day: string]: {
    start: string;
    end: string;
    isWorking: boolean;
  };
};

// Extended appointment type with joined data
export type AppointmentWithDetails = Appointment & {
  trainer?: Trainer;
  service?: Service;
};

// Extended flex package types with joined data
export type MemberFlexPackageWithDetails = MemberFlexPackage & {
  flex_package?: FlexPackage;
  member?: Member;
};

export type FlexPackageSessionWithDetails = FlexPackageSession & {
  member_package?: MemberFlexPackageWithDetails;
  member?: Member;
};

// Package status types
export type FlexPackageStatus =
  | "active"
  | "expired"
  | "completed"
  | "cancelled"
  | "paused";
export type FlexSessionStatus = "scheduled" | "completed";

// Appointment status enum values
export type AppointmentStatus =
  | "scheduled"
  | "in-progress"
  | "completed"
  | "cancelled";

// Trainer status enum values
export type TrainerStatus = "active" | "inactive";
